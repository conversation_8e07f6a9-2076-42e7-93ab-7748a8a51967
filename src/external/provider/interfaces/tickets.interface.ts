export interface CreateNewTicket {
  // Core ticket fields
  title: string;
  description?: string;
  teamId: string;
  requestorEmail: string;

  // Status and workflow fields
  statusId?: string;
  priorityId?: string;
  typeId?: string;

  // Assignment fields
  assignedAgentId?: string;
  submitterEmail?: string;

  // Organization fields
  accountId?: string;
  subTeamId?: string;

  // Metadata fields
  sentimentId?: string;
  dueDate?: string;
  urgency?: string;

  // System fields
  performRouting?: boolean;
  formId?: string;
  customFieldValues?: Array<{
    customFieldId: string;
    data: Array<{ value: any }>;
    metadata: Record<string, any>;
  }>;

  metadata: {
    slack: { channel: string; ts: string; user: string };
    slackTeamId?: string;
  };

  // New comment fields
  commentContentHtml?: string;
  commentContent?: string;
  commentContentJson?: string;
  commentAttachmentIds?: string[];
  commentMetadata?: Record<string, any>;
  commentImpersonatedUserName?: string;
  commentImpersonatedUserEmail?: string;
  commentImpersonatedUserAvatar?: string;
}

export interface UpdateTicketData {
  statusId: string;
  priorityId: string;
  assignedAgentId: string;
}
