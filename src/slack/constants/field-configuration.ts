/**
 * Centralized field configuration for form submission and ticket creation
 * This file defines all standard and custom field mappings, types, and processing rules
 */

export enum FieldCategory {
  STANDARD = 'standard',
  CUSTOM = 'custom',
  THENA_RESTRICTED = 'thena_restricted',
}

export enum StandardFieldType {
  // Core ticket fields
  TITLE = 'title',
  DESCRIPTION = 'description',
  REQUESTOR_EMAIL = 'requestorEmail',
  TEAM_ID = 'teamId',
  
  // Status and workflow fields
  STATUS_ID = 'statusId',
  PRIORITY_ID = 'priorityId',
  TYPE_ID = 'typeId',
  
  // Assignment fields
  ASSIGNED_AGENT_ID = 'assignedAgentId',
  SUBMITTER_EMAIL = 'submitterEmail',
  
  // Organization fields
  ACCOUNT_ID = 'accountId',
  SUB_TEAM_ID = 'subTeamId',
  
  // Metadata fields
  SENTIMENT_ID = 'sentimentId',
  DUE_DATE = 'dueDate',
  URGENCY = 'urgency',
  
  // Form specific
  FORM_ID = 'formId',
}

export interface FieldMappingRule {
  /** The field name as it appears in forms (case-insensitive) */
  formFieldNames: string[];
  /** The target field name for API submission */
  apiFieldName: StandardFieldType;
  /** Whether this field is required for ticket creation */
  required: boolean;
  /** Whether this field should be included in the ticket payload */
  includeInTicketPayload: boolean;
  /** Whether this field should be included in custom fields */
  includeInCustomFields: boolean;
  /** Data type for validation */
  dataType: 'string' | 'number' | 'boolean' | 'array' | 'date';
  /** Default value if not provided */
  defaultValue?: any;
  /** Validation rules */
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    customValidator?: (value: any) => boolean;
  };
}

/**
 * Comprehensive field mapping configuration
 * Maps form field names to their corresponding API field names and processing rules
 */
export const FIELD_MAPPING_CONFIG: Record<string, FieldMappingRule> = {
  // Core ticket fields
  title: {
    formFieldNames: ['title', 'subject', 'summary'],
    apiFieldName: StandardFieldType.TITLE,
    required: true,
    includeInTicketPayload: true,
    includeInCustomFields: false,
    dataType: 'string',
    validation: {
      minLength: 1,
      maxLength: 255,
    },
  },
  
  description: {
    formFieldNames: ['description', 'details', 'content', 'body'],
    apiFieldName: StandardFieldType.DESCRIPTION,
    required: false,
    includeInTicketPayload: true,
    includeInCustomFields: false,
    dataType: 'string',
  },
  
  requestorEmail: {
    formFieldNames: ['requestoremail', 'requester', 'requestor', 'customer_email'],
    apiFieldName: StandardFieldType.REQUESTOR_EMAIL,
    required: true,
    includeInTicketPayload: true,
    includeInCustomFields: false,
    dataType: 'string',
    validation: {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    },
  },
  
  // Status and workflow fields
  statusId: {
    formFieldNames: ['status', 'statusid', 'ticket_status'],
    apiFieldName: StandardFieldType.STATUS_ID,
    required: false,
    includeInTicketPayload: true,
    includeInCustomFields: false,
    dataType: 'string',
  },
  
  priorityId: {
    formFieldNames: ['priority', 'priorityid', 'ticket_priority'],
    apiFieldName: StandardFieldType.PRIORITY_ID,
    required: false,
    includeInTicketPayload: true,
    includeInCustomFields: false,
    dataType: 'string',
  },
  
  typeId: {
    formFieldNames: ['type', 'typeid', 'ticket_type', 'category'],
    apiFieldName: StandardFieldType.TYPE_ID,
    required: false,
    includeInTicketPayload: true,
    includeInCustomFields: false,
    dataType: 'string',
  },
  
  // Assignment fields
  assignedAgentId: {
    formFieldNames: ['assignee', 'assigned_agent', 'agent', 'assigned_to', 'assignedagentid'],
    apiFieldName: StandardFieldType.ASSIGNED_AGENT_ID,
    required: false,
    includeInTicketPayload: true,
    includeInCustomFields: false,
    dataType: 'string',
  },
  
  submitterEmail: {
    formFieldNames: ['submitter', 'submitteremail', 'submitted_by'],
    apiFieldName: StandardFieldType.SUBMITTER_EMAIL,
    required: false,
    includeInTicketPayload: true,
    includeInCustomFields: false,
    dataType: 'string',
    validation: {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    },
  },
  
  // Organization fields
  accountId: {
    formFieldNames: ['account', 'accountid', 'customer_account'],
    apiFieldName: StandardFieldType.ACCOUNT_ID,
    required: false,
    includeInTicketPayload: true,
    includeInCustomFields: false,
    dataType: 'string',
  },
  
  subTeamId: {
    formFieldNames: ['subteam', 'subteamid', 'sub_team', 'group'],
    apiFieldName: StandardFieldType.SUB_TEAM_ID,
    required: false,
    includeInTicketPayload: true,
    includeInCustomFields: false,
    dataType: 'string',
  },
  
  // Metadata fields
  sentimentId: {
    formFieldNames: ['sentiment', 'sentimentid', 'mood'],
    apiFieldName: StandardFieldType.SENTIMENT_ID,
    required: false,
    includeInTicketPayload: true,
    includeInCustomFields: false,
    dataType: 'string',
  },
  
  dueDate: {
    formFieldNames: ['due', 'due_date', 'duedate', 'deadline'],
    apiFieldName: StandardFieldType.DUE_DATE,
    required: false,
    includeInTicketPayload: true,
    includeInCustomFields: false,
    dataType: 'date',
  },
  
  urgency: {
    formFieldNames: ['urgency', 'urgent'],
    apiFieldName: StandardFieldType.URGENCY,
    required: false,
    includeInTicketPayload: true,
    includeInCustomFields: false,
    dataType: 'string',
  },
};

/**
 * Reserved field names that should not be processed as custom fields
 */
export const RESERVED_FIELD_NAMES = new Set([
  'requestoremail',
  'teamid', 
  'formid',
  'metadata',
  ...Object.values(FIELD_MAPPING_CONFIG).flatMap(config => config.formFieldNames),
]);

/**
 * Fields that should be explicitly controlled in ticket creation payload
 */
export const CONTROLLED_TICKET_FIELDS = [
  'title',
  'requestorEmail',
  'teamId',
  'text',
  'description',
  'statusId',
  'priorityId',
  'typeId',
  'assignedAgentId',
  'submitterEmail',
  'accountId',
  'subTeamId',
  'sentimentId',
  'dueDate',
  'urgency',
  'performRouting',
  'formId',
  'customFieldValues',
  'metadata',
] as const;

export type ControlledTicketField = typeof CONTROLLED_TICKET_FIELDS[number];
