import { Inject, Injectable } from '@nestjs/common';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../../../utils';

import { SlackView } from '../../../decorators/slack-view.decorator';
import { DecoratedSlackViewMiddlewareArgs } from '../../../event-handlers';
import { SlackActionHandler } from '../../../interfaces/action-handler.interface';
import { ViewSubmissionPayload } from '../../../interfaces/slack-view-submission.interface';
import { FieldProcessorService } from '../../../services/field-processor.service';
import { FormBuilderService } from '../../../services/form-builder.service';
import { FormSubmissionService } from '../../../services/form-submission.service';
import { NotificationService } from '../../../services/notification.service';
import { TicketCreationHelper } from '../../../services/ticket-creation-helper.service';

import { validateFieldValue } from '../../../utils/form-field-validation.utils';
import { FORM_FIELD_BLOCK_ID_PREFIX } from './form-field-action.handler';

// Constants
export const FORM_SUBMISSION_MODAL_CALLBACK_ID = 'form_submission_modal';
const LOG_SPAN = 'FormSubmissionHandler';

/**
 * Handles form submission view events
 * Processes form data and creates tickets
 */
@Injectable()
@SlackView(FORM_SUBMISSION_MODAL_CALLBACK_ID)
export class FormSubmissionHandler implements SlackActionHandler {
  constructor(
    @Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger,
    private readonly formSubmissionService: FormSubmissionService,
    private readonly fieldProcessorService: FieldProcessorService,
    private readonly formBuilderService: FormBuilderService,
    private readonly notificationService: NotificationService,
    private readonly ticketCreationHelper: TicketCreationHelper,
  ) {}

  /**
   * Handles form submission
   * Validates form data and creates a ticket
   */
  async handle(args: DecoratedSlackViewMiddlewareArgs) {
    this.logger.log(`${LOG_SPAN} Handling form submission`);

    try {
      const viewAction = args.body as unknown as ViewSubmissionPayload;
      const metadata = JSON.parse(viewAction.view.private_metadata);
      const { formId, teamId, channelId } = metadata;
      const { installation } = args.context;

      // Fetch the form definition
      const formData = await this.fetchFormDefinition(
        installation,
        formId,
        teamId,
      );
      if (!formData) {
        return this.createErrorResponse(
          'block_0',
          'Failed to load form definition. Please try again.',
        );
      }

      // Process form submission to get submissions
      const submissions = this.formSubmissionService.processSubmission(
        viewAction,
        formData.fields,
      );

      // Use the new field processor to handle all field types dynamically
      const processedData = this.fieldProcessorService.processFormSubmissions(
        submissions,
        formData.fields,
      );

      // Validate using the old validation method as well for backward compatibility
      const validationErrors =
        this.formSubmissionService.validateSubmission(submissions);
      const fieldErrors = this.validateFieldByField(submissions);

      // Combine all validation errors
      const allErrors = {
        ...processedData.validationErrors,
        ...fieldErrors,
      };

      // Add any general validation errors
      if (validationErrors.length > 0) {
        allErrors.block_0 = validationErrors.join('; ');
      }

      if (Object.keys(allErrors).length > 0) {
        return { response_action: 'errors', errors: allErrors };
      }

      // Check for missing required fields
      const missingFields = this.fieldProcessorService.getMissingRequiredFields(
        processedData.standardFields,
      );
      if (missingFields.length > 0) {
        this.logger.error(
          `${LOG_SPAN} Missing required fields: ${missingFields.join(', ')}`,
        );
        return this.createErrorResponse(
          'block_0',
          `Missing required fields: ${missingFields.join(', ')}`,
        );
      }

      const { customFieldValues, standardFields } = processedData;

      // Follow the same pattern as createTicketDirectly
      // 1. First create the ticket on the platform
      // 2. Then post the conversation thread to the platform
      // 3. Finally handle any reactions (not applicable for form submission)

      return await this.createTicketAndPostThread(
        viewAction,
        metadata,
        installation,
        standardFields,
        customFieldValues,
        standardFields,
        channelId,
        teamId,
        formId,
        args.context.client,
      );
    } catch (error) {
      this.logError(error);
      // Let Bolt handle the error response if it's a systemic issue
      throw error;
    }
  }

  /**
   * Fetches form definition from the service
   */
  private async fetchFormDefinition(
    installation: any,
    formId: string,
    teamId: string,
  ) {
    const fullFormData = await this.formBuilderService.getFormById(
      installation,
      formId,
      teamId,
    );

    if (!fullFormData || !fullFormData.fields) {
      this.logger.error(
        `${LOG_SPAN} Failed to fetch form definition for ID: ${formId}`,
      );
      return null;
    }

    return fullFormData;
  }

  /**
   * Creates a standardized error response
   */
  private createErrorResponse(blockId: string, message: string) {
    return {
      response_action: 'errors',
      errors: {
        [blockId]: message,
      },
    };
  }

  /**
   * Validates each field individually
   */
  private validateFieldByField(submissions: any[]) {
    const fieldErrors: Record<string, string> = {};

    for (const submission of submissions) {
      const { field, value } = submission;
      const validationResult = validateFieldValue(field, value);

      if (!validationResult.isValid && validationResult.errorMessage) {
        fieldErrors[`${FORM_FIELD_BLOCK_ID_PREFIX}${field.id}`] =
          validationResult.errorMessage;
        this.logger.debug(
          `${LOG_SPAN} Field validation error for ${field.id} (${field.name}): ${validationResult.errorMessage}`,
        );
      }
    }

    return fieldErrors;
  }

  /**
   * Creates a ticket and posts the conversation thread using the TicketCreationHelper
   * 1. First create the ticket on the platform
   * 2. Then post the conversation thread to the platform
   */
  private async createTicketAndPostThread(
    viewAction: ViewSubmissionPayload,
    metadata: any,
    installation: any,
    processedStandardFields: Record<string, any>,
    customFieldValues: any[],
    _legacyStandardFields: Record<string, any>, // Keep for backward compatibility but not used
    channelId: string,
    teamId: string,
    formId: string,
    client: any,
  ) {
    try {
      const userId = viewAction.user?.id || '';
      const slackTeamId = viewAction.team?.id || installation.teamId;
      const messageTs = metadata.ts || metadata.messageTs || metadata.threadTs;
      const fallbackTs = `${Math.floor(Date.now() / 1000)}.${Date.now() % 1000}`;
      const slackTs = messageTs || fallbackTs;

      this.logger.debug(
        `${LOG_SPAN} Context: User ID: ${userId}, Channel ID: ${channelId}, Team ID: ${slackTeamId}, Timestamp: ${slackTs} (${messageTs ? 'original' : 'fallback'})`,
      );

      // Check if ticket exists (dummy check kept for compatibility)
      const ticketExists = false;
      if (ticketExists) {
        return await this.handleExistingTicket(
          client,
          channelId,
          userId,
          { id: 'dummy-id', title: 'Dummy Ticket' },
          metadata.threadTs || slackTs,
          teamId,
        );
      }

      // STEP 1: Create the ticket on the platform using TicketCreationHelper
      const ticketPayload = {
        // Core required fields
        teamId: teamId,
        requestorEmail: processedStandardFields.requestorEmail,
        title: processedStandardFields.title,
        text: processedStandardFields.description || '',
        description: processedStandardFields.description || '',

        // All other standard fields from the processor
        ...processedStandardFields,

        // System fields
        formId,
        customFieldValues,
        metadata: {
          slack: { ts: slackTs, user: userId, channel: channelId },
          slackTeamId: slackTeamId,
        },
      };

      const ticket = await this.ticketCreationHelper.createTicketWithMetadata(
        installation,
        ticketPayload,
      );

      this.logger.debug(`${LOG_SPAN} Ticket created from form: ${ticket.id}`);

      // STEP 2: Post the conversation thread to the platform (if we have a thread timestamp)
      if (
        messageTs &&
        messageTs !== 'SLASH_TICKET' &&
        messageTs !== fallbackTs
      ) {
        await this.ticketCreationHelper.postConversationThreadToPlatform(
          installation,
          ticket,
          {
            channelId,
            messageTs,
            userId,
            shouldProcessReactions: true,
            shouldSendConfirmation: true,
          },
        );
      } else {
        // No thread to process, just send confirmation
        await this.notificationService.sendTicketCreationConfirmation(
          client,
          channelId,
          userId,
          {
            id: ticket.id,
            title: ticket.title || processedStandardFields.title,
            teamIdentifier: ticket.teamIdentifier,
            ticketId: ticket.ticketId,
          },
          messageTs,
          teamId,
        );
      }

      return { response_action: 'clear' };
    } catch (error) {
      return this.handleTicketCreationError(error);
    }
  }

  /**
   * Handles the case where a ticket already exists
   */
  private async handleExistingTicket(
    client: any,
    channelId: string,
    userId: string,
    existingTicket: any,
    threadTs: string,
    teamId: string,
  ) {
    this.logger.log(
      `${LOG_SPAN} Ticket already exists with ID: ${existingTicket.id}`,
    );
    try {
      await this.notificationService.sendTicketAlreadyExistsNotification(
        client,
        channelId,
        userId,
        existingTicket,
        threadTs,
        teamId,
      );
    } catch (msgError) {
      this.logger.error(
        `${LOG_SPAN} Error sending existing ticket notification: ${msgError}`,
      );
    }
    return { response_action: 'clear' };
  }

  /**
   * Handles errors during ticket creation
   */
  private handleTicketCreationError(error: any) {
    this.logger.error(
      `${LOG_SPAN} Error creating ticket: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );

    // Map error to a field if possible, or use a generic block_id
    const firstFieldId = 'block_0';
    return {
      response_action: 'errors',
      errors: {
        [firstFieldId]:
          error instanceof Error ? error.message : 'Failed to create ticket',
      },
    };
  }

  /**
   * Generic error logger
   */
  private logError(error: unknown): void {
    if (error instanceof Error) {
      this.logger.error(
        `${LOG_SPAN} Error processing form submission: ${error.message}`,
      );
    } else {
      this.logger.error(
        `${LOG_SPAN} Error processing form submission: ${JSON.stringify(error)}`,
      );
    }
  }
}
