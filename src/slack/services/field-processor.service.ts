import { Inject, Injectable } from '@nestjs/common';
import { CUSTOM_LOGGER_TOKEN, ILogger } from '../../utils';
import { Field } from '../blocks/components/composite/form-builder/conditional-form-builder.composite';
import {
  FIELD_MAPPING_CONFIG,
  FieldCategory,
  FieldMappingRule,
} from '../constants/field-configuration';
import { FormFieldSubmission } from './form-submission.service';

export interface ProcessedFormData {
  standardFields: Record<string, any>;
  customFieldValues: Array<{
    customFieldId: string;
    data: Array<{ value: any }>;
    metadata: Record<string, any>;
  }>;
  validationErrors: Record<string, string>;
}

export interface FieldClassification {
  category: FieldCategory;
  mappingRule?: FieldMappingRule;
  isCustomField: boolean;
  isThenaRestricted: boolean;
}

const LOG_SPAN = 'FieldProcessorService';

@Injectable()
export class FieldProcessorService {
  constructor(@Inject(CUSTOM_LOGGER_TOKEN) private readonly logger: ILogger) {}

  /**
   * Process form submissions into standardized format for ticket creation
   */
  processFormSubmissions(
    submissions: FormFieldSubmission[],
    formFields: Field[],
  ): ProcessedFormData {
    this.logger.debug(
      `${LOG_SPAN} Processing ${submissions.length} form submissions`,
    );

    const result: ProcessedFormData = {
      standardFields: {},
      customFieldValues: [],
      validationErrors: {},
    };

    const fieldMap = new Map(formFields.map((field) => [field.id, field]));

    for (const submission of submissions) {
      try {
        const field = fieldMap.get(submission.field.id);
        if (!field) {
          this.logger.warn(
            `${LOG_SPAN} Field not found: ${submission.field.id}`,
          );
          continue;
        }

        const classification = this.classifyField(field);

        switch (classification.category) {
          case FieldCategory.STANDARD:
            if (classification.mappingRule) {
              this.processStandardField(
                submission,
                classification.mappingRule,
                result,
              );
            }
            break;
          case FieldCategory.CUSTOM:
          case FieldCategory.THENA_RESTRICTED:
            this.processCustomField(submission, field, result);
            break;
        }
      } catch (error) {
        this.logger.error(
          `${LOG_SPAN} Error processing field ${submission.field.id}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
        result.validationErrors[submission.field.id] =
          'Processing error occurred';
      }
    }

    this.logger.debug(
      `${LOG_SPAN} Processed fields - Standard: ${Object.keys(result.standardFields).length}, Custom: ${result.customFieldValues.length}, Errors: ${Object.keys(result.validationErrors).length}`,
    );

    return result;
  }

  /**
   * Classify a field to determine how it should be processed
   */
  private classifyField(field: Field): FieldClassification {
    const fieldNameLower = field.name.toLowerCase();
    const fieldId = field.id;

    // Check if it's a custom field (starts with CF)
    if (fieldId.startsWith('CF')) {
      return {
        category: FieldCategory.CUSTOM,
        isCustomField: true,
        isThenaRestricted: false,
      };
    }

    // Check if it's a Thena restricted field (non-CF but has specific metadata)
    if (field.metadata?.originalType || this.isThenaRestrictedField(field)) {
      return {
        category: FieldCategory.THENA_RESTRICTED,
        isCustomField: false,
        isThenaRestricted: true,
      };
    }

    // Check if it's a standard field by looking up the mapping configuration
    const mappingRule = this.findMappingRule(fieldNameLower);
    if (mappingRule) {
      return {
        category: FieldCategory.STANDARD,
        mappingRule,
        isCustomField: false,
        isThenaRestricted: false,
      };
    }

    // Default to custom field if no other classification matches
    return {
      category: FieldCategory.CUSTOM,
      isCustomField: true,
      isThenaRestricted: false,
    };
  }

  /**
   * Find the mapping rule for a field name
   */
  private findMappingRule(
    fieldNameLower: string,
  ): FieldMappingRule | undefined {
    for (const [, rule] of Object.entries(FIELD_MAPPING_CONFIG)) {
      if (
        rule.formFieldNames.some(
          (name) => name.toLowerCase() === fieldNameLower,
        )
      ) {
        return rule;
      }
    }
    return undefined;
  }

  /**
   * Check if a field is a Thena restricted field
   */
  private isThenaRestrictedField(field: Field): boolean {
    // Add logic to identify Thena restricted fields
    // This could be based on field metadata, naming conventions, or other criteria
    const thenaRestrictedNames = [
      'status',
      'priority',
      'assignee',
      'type',
      'account',
    ];
    return thenaRestrictedNames.includes(field.name.toLowerCase());
  }

  /**
   * Process a standard field according to its mapping rule
   */
  private processStandardField(
    submission: FormFieldSubmission,
    mappingRule: FieldMappingRule,
    result: ProcessedFormData,
  ): void {
    const { value } = submission;
    const apiFieldName = mappingRule.apiFieldName;

    // Validate the field value
    const validationError = this.validateFieldValue(value, mappingRule);
    if (validationError) {
      result.validationErrors[submission.field.id] = validationError;
      return;
    }

    // Process and transform the value if needed
    const processedValue = this.transformFieldValue(value, mappingRule);

    if (mappingRule.includeInTicketPayload) {
      result.standardFields[apiFieldName] = processedValue;
      this.logger.debug(
        `${LOG_SPAN} Mapped standard field: ${submission.field.name} -> ${apiFieldName} = ${processedValue}`,
      );
    }
  }

  /**
   * Process a custom field
   */
  private processCustomField(
    submission: FormFieldSubmission,
    field: Field,
    result: ProcessedFormData,
  ): void {
    const customFieldId = field.id.startsWith('CF')
      ? field.id
      : `CF_${field.id}`;
    const { value } = submission;

    // Handle special cases for different field types
    const processedValue = this.processCustomFieldValue(value, field);

    result.customFieldValues.push({
      customFieldId: customFieldId.replace('CF_', ''),
      data: Array.isArray(processedValue)
        ? processedValue.map((v) => ({ value: this.sanitizeValue(v) }))
        : [{ value: this.sanitizeValue(processedValue) }],
      metadata: {},
    });

    this.logger.debug(
      `${LOG_SPAN} Processed custom field: ${field.name} (${customFieldId}) = ${processedValue}`,
    );
  }

  /**
   * Validate a field value against its mapping rule
   */
  private validateFieldValue(
    value: any,
    mappingRule: FieldMappingRule,
  ): string | null {
    // Check required fields
    if (
      mappingRule.required &&
      (value === null || value === undefined || value === '')
    ) {
      return 'This field is required';
    }

    // Skip validation for empty non-required fields
    if (
      !mappingRule.required &&
      (value === null || value === undefined || value === '')
    ) {
      return null;
    }

    // Validate based on data type
    switch (mappingRule.dataType) {
      case 'string':
        if (typeof value !== 'string') {
          return 'Must be a text value';
        }
        break;
      case 'number':
        if (typeof value !== 'number' && Number.isNaN(Number(value))) {
          return 'Must be a number';
        }
        break;
      case 'boolean':
        if (
          typeof value !== 'boolean' &&
          value !== 'true' &&
          value !== 'false'
        ) {
          return 'Must be a boolean value';
        }
        break;
      case 'array':
        if (!Array.isArray(value)) {
          return 'Must be an array';
        }
        break;
    }

    // Apply validation rules
    if (mappingRule.validation) {
      const validation = mappingRule.validation;

      if (
        validation.pattern &&
        typeof value === 'string' &&
        !validation.pattern.test(value)
      ) {
        return 'Invalid format';
      }

      if (
        validation.minLength &&
        typeof value === 'string' &&
        value.length < validation.minLength
      ) {
        return `Must be at least ${validation.minLength} characters`;
      }

      if (
        validation.maxLength &&
        typeof value === 'string' &&
        value.length > validation.maxLength
      ) {
        return `Must be no more than ${validation.maxLength} characters`;
      }

      if (validation.customValidator && !validation.customValidator(value)) {
        return 'Invalid value';
      }
    }

    return null;
  }

  /**
   * Transform field value according to its data type
   */
  private transformFieldValue(value: any, mappingRule: FieldMappingRule): any {
    if (value === null || value === undefined) {
      return mappingRule.defaultValue || null;
    }

    switch (mappingRule.dataType) {
      case 'number':
        return typeof value === 'number' ? value : Number(value);
      case 'boolean':
        return typeof value === 'boolean' ? value : value === 'true';
      case 'date':
        return value instanceof Date
          ? value.toISOString()
          : new Date(value).toISOString();
      default:
        return value;
    }
  }

  /**
   * Process custom field values with type-specific handling
   */
  private processCustomFieldValue(value: any, field: Field): any {
    const originalType =
      field.metadata?.originalType?.toLowerCase() || field.type.toLowerCase();

    // Handle datetime fields specially
    if (originalType === 'date_time' || originalType === 'datetime') {
      if (value === '' || value === null || value === undefined) {
        return '';
      }

      try {
        const date = new Date(value);
        return date.toISOString().split('.')[0];
      } catch {
        return '';
      }
    }

    return value;
  }

  /**
   * Sanitize a value for safe storage
   */
  private sanitizeValue(value: any): string {
    if (value === null || value === undefined) {
      return '';
    }
    return String(value);
  }

  /**
   * Get all required standard fields that are missing
   */
  getMissingRequiredFields(standardFields: Record<string, any>): string[] {
    const missing: string[] = [];

    for (const [, rule] of Object.entries(FIELD_MAPPING_CONFIG)) {
      if (rule.required && !standardFields[rule.apiFieldName]) {
        missing.push(rule.apiFieldName);
      }
    }

    return missing;
  }
}
